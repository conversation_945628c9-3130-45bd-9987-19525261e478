# PFG Log Reader Module for Magento 1.9

A comprehensive log management module for Magento 1.9 that provides secure access to system logs and exception files through the admin panel.

## Features

### 📋 **Log File Management**
- **Discover & Display**: Automatically discovers log files from `var/log/` directory
- **File Viewer**: View last 50 lines of any log file with AJAX loading
- **Pagination**: Efficient pagination for directories with 20+ files
- **File Information**: Display file size, modification date, and permissions

### 🚨 **Exception File Management**
- **Exception Discovery**: Scans `var/report/` directory for exception files
- **Structured Display**: Shows exception details, stack traces, and timestamps
- **File Metadata**: Displays file size and creation date

### 🗑️ **Secure File Operations**
- **Individual Deletion**: Delete specific log or exception files
- **Bulk Operations**: Delete all files with enhanced confirmation dialogs
- **Progress Feedback**: Real-time progress updates during bulk operations
- **Security Validation**: Comprehensive path traversal prevention

### 🔒 **Security Features**
- **Admin Authentication**: Requires admin login and proper ACL permissions
- **CSRF Protection**: Form key validation for all AJAX requests
- **Rate Limiting**: Prevents abuse with Redis-based rate limiting
- **Input Validation**: Comprehensive file path and parameter validation
- **Error Sanitization**: Prevents sensitive information exposure

### ⚡ **Performance Optimizations**
- **Optimized File Discovery**: Memory-efficient directory scanning
- **Early Pagination**: Avoids loading all files into memory
- **AJAX Loading**: Non-blocking file content loading
- **Responsive Design**: Mobile-friendly admin interface

## Installation

### Requirements
- Magento 1.9.x
- PHP 5.6+ (recommended PHP 7.4)
- Redis (optional, for rate limiting)

### Installation Steps

1. **Download the module**
   ```bash
   git clone https://bitbucket.org/pfg/log-reader-module.git
   cd log-reader-module
   ```

2. **Copy files to Magento**
   ```bash
   cp -r app/* /path/to/magento/app/
   ```

3. **Clear cache**
   ```bash
   rm -rf /path/to/magento/var/cache/*
   ```

4. **Verify installation**
   - Login to Magento Admin
   - Navigate to System > Configuration > PFG > Log Reader
   - Enable the module and configure settings

## Configuration

### Admin Configuration Path
**System > Configuration > PFG > Log Reader**

### Configuration Options

#### General Settings
- **Enable Module**: Enable/disable the Log Reader functionality
- **Max File Size**: Maximum file size to display (default: 10MB)
- **Lines to Display**: Number of lines to show (default: 50)

#### Security Settings
- **Rate Limiting**: Enable/disable request rate limiting
- **Max Requests**: Maximum requests per minute (default: 60)
- **Session Timeout**: Admin session timeout for log access

#### Performance Settings
- **Cache TTL**: Cache time-to-live for file listings (default: 300 seconds)
- **Pagination Limit**: Files per page (default: 20)

## Usage

### Accessing the Log Reader
1. Login to Magento Admin
2. Navigate to **System > Configuration > PFG > Log Reader**
3. Enable the module in General Settings
4. The log viewer interface will appear below the configuration

### Viewing Log Files
1. Select the **Log Files** tab
2. Click on any file to view its last 50 lines
3. Use pagination to browse through multiple files
4. File content loads via AJAX without page refresh

### Managing Exception Files
1. Select the **Exception Files** tab
2. View exception details including stack traces
3. Click on files to see full exception information
4. Monitor system errors and debugging information

### File Operations
- **Delete Individual Files**: Click the delete button next to any file
- **Delete All Files**: Use "Delete All" button with enhanced confirmation
- **Bulk Operations**: Progress feedback during mass deletions

## Security Considerations

### Access Control
- Module requires admin authentication
- ACL permissions: `system/config/pfg/log_reader`
- Session validation on every request

### File Security
- Path traversal prevention
- File type validation
- Size limit enforcement
- Secure file deletion

### Rate Limiting
- Redis-based request limiting
- Configurable request thresholds
- Automatic blocking of excessive requests

## Troubleshooting

### Common Issues

**Module not appearing in admin**
- Clear Magento cache: `rm -rf var/cache/*`
- Check file permissions
- Verify module is enabled in `app/etc/modules/PFG_LogReader.xml`

**Files not loading**
- Check `var/log/` and `var/report/` directory permissions
- Verify Redis connection (if rate limiting enabled)
- Check error logs in `var/log/pfg_logreader.log`

**Permission denied errors**
- Ensure web server has read access to log directories
- Check Magento file permissions
- Verify admin user has proper ACL permissions

### Error Logging
Module errors are logged to: `var/log/pfg_logreader.log`

## Technical Details

### File Structure
```
app/
├── code/local/PFG/LogReader/
│   ├── Block/Adminhtml/System/Config/Form/Fieldset/
│   ├── controllers/Adminhtml/Pfg/
│   ├── etc/
│   ├── Helper/
│   └── Model/
└── etc/modules/PFG_LogReader.xml
```

### Key Components
- **Controller**: `LogreaderController.php` - Handles AJAX requests
- **Block**: `Logviewer.php` - Renders admin interface
- **Models**: File operations, security validation
- **Helper**: Configuration and logging utilities

## Support

For issues, feature requests, or contributions:
- **Repository**: https://bitbucket.org/pfg/log-reader-module
- **Documentation**: See `/docs` directory
- **Issues**: Use Bitbucket issue tracker

## License

This module is proprietary software developed by PFG Development Team.
All rights reserved.

## Version History

See [CHANGELOG.md](CHANGELOG.md) for detailed version history.

<?php
/**
 * Test script for PFG Log Reader functionality
 */

require_once 'app/Mage.php';
Mage::app();

echo "Testing PFG Log Reader Module...\n\n";

// Test File Model
$fileModel = Mage::getModel('pfg_logreader/file');

// Test log files discovery
echo "1. Testing Log Files Discovery:\n";
$logFiles = $fileModel->discoverLogFiles(1, 5);
echo "Found " . count($logFiles['files']) . " log files\n";
echo "Pagination: Page " . $logFiles['pagination']['current_page'] . " of " . $logFiles['pagination']['total_pages'] . "\n";
if (!empty($logFiles['files'])) {
    echo "First file: " . $logFiles['files'][0]['name'] . " (" . $logFiles['files'][0]['size_formatted'] . ")\n";
}
echo "\n";

// Test exception files discovery
echo "2. Testing Exception Files Discovery:\n";
$exceptionFiles = $fileModel->discoverExceptionFiles(1, 5);
echo "Found " . count($exceptionFiles['files']) . " exception files\n";
echo "Pagination: Page " . $exceptionFiles['pagination']['current_page'] . " of " . $exceptionFiles['pagination']['total_pages'] . "\n";
if (!empty($exceptionFiles['files'])) {
    echo "First file: " . $exceptionFiles['files'][0]['name'] . " (" . $exceptionFiles['files'][0]['size_formatted'] . ")\n";
}
echo "\n";

// Test large file handling
echo "3. Testing Large File Handling:\n";
$largeLogFile = 'var/log/test_large_file.log';
if (file_exists($largeLogFile)) {
    $result = $fileModel->readFileContent($largeLogFile, 50);
    echo "Large file read test: " . (isset($result['content']) ? 'SUCCESS' : 'FAILED') . "\n";
    echo "Lines returned: " . (isset($result['lines_returned']) ? $result['lines_returned'] : 'N/A') . "\n";
    echo "Total lines: " . (isset($result['total_lines']) ? $result['total_lines'] : 'N/A') . "\n";
    echo "File size: " . (isset($result['file_size_formatted']) ? $result['file_size_formatted'] : 'N/A') . "\n";
} else {
    echo "Large test file not found\n";
}
echo "\n";

// Test Security Model
echo "4. Testing Security Validation:\n";
$securityModel = Mage::getModel('pfg_logreader/security');
$testPaths = [
    'var/log/system.log',
    'var/report/1234567890123',
    '../../../etc/passwd',
    'var/log/../../../etc/passwd'
];

foreach ($testPaths as $path) {
    $result = $securityModel->validateFileAccess($path);
    echo "Path: $path - " . ($result['allowed'] ? 'ALLOWED' : 'DENIED (' . $result['reason'] . ')') . "\n";
}
echo "\n";

// Test Helper
echo "5. Testing Helper Functions:\n";
$helper = Mage::helper('pfg_logreader');
echo "Log directory: " . $helper->getLogDirectory() . "\n";
echo "Report directory: " . $helper->getReportDirectory() . "\n";
echo "Max file size: " . $helper->getMaxFileSize() . " bytes\n";
echo "Max lines: " . $helper->getMaxLines() . "\n";
echo "\n";

echo "All tests completed!\n";
?>

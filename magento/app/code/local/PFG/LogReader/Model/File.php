<?php
/**
 * PFG Log Reader File Model
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log File Model
 *
 * Handles file discovery, scanning, and content reading operations
 * for log files in var/log and var/report directories.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Model_File extends Mage_Core_Model_Abstract
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_helper = Mage::helper('pfg_logreader');
    }

    /**
     * Discover all log files from both directories
     *
     * @param int $page Current page number (1-based)
     * @param int $limit Files per page
     * @return array
     */
    public function discoverFiles($page = 1, $limit = null)
    {
        if ($limit === null) {
            $limit = $this->_helper->getFilesPerPage();
        }

        try {
            $allFiles = array();
            
            // Scan log directory
            $logFiles = $this->_scanDirectory($this->_helper->getLogDirectory(), false);
            foreach ($logFiles as $file) {
                $file['type'] = 'log';
                $allFiles[] = $file;
            }

            // Scan report directory recursively
            $reportFiles = $this->_scanDirectory($this->_helper->getReportDirectory(), true);
            foreach ($reportFiles as $file) {
                $file['type'] = 'report';
                $allFiles[] = $file;
            }

            // Sort files by modification time (newest first)
            usort($allFiles, function($a, $b) {
                return $b['modified'] - $a['modified'];
            });

            // Calculate pagination
            $totalFiles = count($allFiles);
            $totalPages = ceil($totalFiles / $limit);
            $offset = ($page - 1) * $limit;
            $files = array_slice($allFiles, $offset, $limit);

            return array(
                'files' => $files,
                'pagination' => array(
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_files' => $totalFiles,
                    'files_per_page' => $limit,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages
                )
            );

        } catch (Exception $e) {
            $this->_helper->logError('Error discovering files', $e);
            return array(
                'files' => array(),
                'pagination' => array(
                    'current_page' => 1,
                    'total_pages' => 0,
                    'total_files' => 0,
                    'files_per_page' => $limit,
                    'has_previous' => false,
                    'has_next' => false
                ),
                'error' => 'Failed to discover log files: ' . $e->getMessage()
            );
        }
    }

    /**
     * Discover log files only from var/log directory
     *
     * @param int $page Current page number (1-based)
     * @param int $limit Files per page
     * @return array
     */
    public function discoverLogFiles($page = 1, $limit = null)
    {
        if ($limit === null) {
            $limit = $this->_helper->getFilesPerPage();
        }

        try {
            // Scan log directory only
            $logFiles = $this->_scanDirectory($this->_helper->getLogDirectory(), false);
            foreach ($logFiles as &$file) {
                $file['type'] = 'log';
            }

            // Sort files by modification time (newest first)
            usort($logFiles, function($a, $b) {
                return $b['modified'] - $a['modified'];
            });

            // Calculate pagination
            $totalFiles = count($logFiles);
            $totalPages = ceil($totalFiles / $limit);
            $offset = ($page - 1) * $limit;
            $files = array_slice($logFiles, $offset, $limit);

            return array(
                'files' => $files,
                'pagination' => array(
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_files' => $totalFiles,
                    'files_per_page' => $limit,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages
                )
            );

        } catch (Exception $e) {
            $this->_helper->logError('Error discovering log files', $e);
            return array(
                'files' => array(),
                'pagination' => array(
                    'current_page' => 1,
                    'total_pages' => 0,
                    'total_files' => 0,
                    'files_per_page' => $limit,
                    'has_previous' => false,
                    'has_next' => false
                ),
                'error' => 'Failed to discover log files: ' . $e->getMessage()
            );
        }
    }

    /**
     * Discover exception files only from var/report directory
     *
     * @param int $page Current page number (1-based)
     * @param int $limit Files per page
     * @return array
     */
    public function discoverExceptionFiles($page = 1, $limit = null)
    {
        if ($limit === null) {
            $limit = $this->_helper->getFilesPerPage();
        }

        try {
            // Scan report directory recursively
            $reportFiles = $this->_scanDirectory($this->_helper->getReportDirectory(), true);
            foreach ($reportFiles as &$file) {
                $file['type'] = 'exception';
            }

            // Sort files by modification time (newest first)
            usort($reportFiles, function($a, $b) {
                return $b['modified'] - $a['modified'];
            });

            // Calculate pagination
            $totalFiles = count($reportFiles);
            $totalPages = ceil($totalFiles / $limit);
            $offset = ($page - 1) * $limit;
            $files = array_slice($reportFiles, $offset, $limit);

            return array(
                'files' => $files,
                'pagination' => array(
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_files' => $totalFiles,
                    'files_per_page' => $limit,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages
                )
            );

        } catch (Exception $e) {
            $this->_helper->logError('Error discovering exception files', $e);
            return array(
                'files' => array(),
                'pagination' => array(
                    'current_page' => 1,
                    'total_pages' => 0,
                    'total_files' => 0,
                    'files_per_page' => $limit,
                    'has_previous' => false,
                    'has_next' => false
                ),
                'error' => 'Failed to discover exception files: ' . $e->getMessage()
            );
        }
    }

    /**
     * Scan directory for files
     *
     * @param string $directory
     * @param bool $recursive
     * @return array
     */
    protected function _scanDirectory($directory, $recursive = false)
    {
        $files = array();

        if (!is_dir($directory) || !is_readable($directory)) {
            $this->_helper->log("Directory not accessible: {$directory}");
            return $files;
        }

        try {
            if ($recursive) {
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
                    RecursiveIteratorIterator::LEAVES_ONLY
                );
            } else {
                $iterator = new DirectoryIterator($directory);
            }

            foreach ($iterator as $fileInfo) {
                if ($fileInfo->isFile() && $this->_isLogFile($fileInfo)) {
                    $filePath = $fileInfo->getPathname();
                    $relativePath = str_replace($directory . DS, '', $filePath);
                    
                    $files[] = array(
                        'name' => $fileInfo->getFilename(),
                        'path' => $filePath,
                        'relative_path' => $relativePath,
                        'size' => $fileInfo->getSize(),
                        'size_formatted' => $this->_helper->formatFileSize($fileInfo->getSize()),
                        'modified' => $fileInfo->getMTime(),
                        'modified_formatted' => date('Y-m-d H:i:s', $fileInfo->getMTime()),
                        'readable' => $this->_helper->isFileReadable($filePath),
                        'directory' => dirname($relativePath) !== '.' ? dirname($relativePath) : ''
                    );
                }
            }

        } catch (Exception $e) {
            $this->_helper->logError("Error scanning directory: {$directory}", $e);
        }

        return $files;
    }

    /**
     * Check if file is a log file
     *
     * @param SplFileInfo $fileInfo
     * @return bool
     */
    protected function _isLogFile($fileInfo)
    {
        $filename = $fileInfo->getFilename();
        
        // Skip hidden files and directories
        if (substr($filename, 0, 1) === '.') {
            return false;
        }

        // Common log file extensions
        $logExtensions = array('log', 'txt', 'out', 'err');
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        if (in_array($extension, $logExtensions)) {
            return true;
        }

        // Files without extension that might be logs
        if (empty($extension)) {
            $logPatterns = array('error', 'access', 'debug', 'system', 'exception');
            foreach ($logPatterns as $pattern) {
                if (stripos($filename, $pattern) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Read last N lines from a file efficiently
     *
     * @param string $filePath
     * @param int $lines
     * @return array
     */
    public function readLastLines($filePath, $lines = null)
    {
        if ($lines === null) {
            $lines = $this->_helper->getLinesToShow();
        }

        try {
            if (!$this->_helper->isFileReadable($filePath)) {
                throw new Exception('File is not readable or exceeds size limit');
            }

            $fileSize = filesize($filePath);
            if ($fileSize === 0) {
                return array(
                    'success' => true,
                    'lines' => array(),
                    'total_lines' => 0,
                    'file_size' => 0
                );
            }

            $handle = fopen($filePath, 'r');
            if (!$handle) {
                throw new Exception('Unable to open file for reading');
            }

            $result = $this->_readLastLinesFromHandle($handle, $lines, $fileSize);
            fclose($handle);

            return array(
                'success' => true,
                'lines' => $result['lines'],
                'total_lines' => $result['total_lines'],
                'file_size' => $fileSize,
                'file_size_formatted' => $this->_helper->formatFileSize($fileSize)
            );

        } catch (Exception $e) {
            $this->_helper->logError("Error reading file: {$filePath}", $e);
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'lines' => array(),
                'total_lines' => 0,
                'file_size' => 0
            );
        }
    }

    /**
     * Read last lines from file handle efficiently
     *
     * @param resource $handle
     * @param int $lines
     * @param int $fileSize
     * @return array
     */
    protected function _readLastLinesFromHandle($handle, $lines, $fileSize)
    {
        $buffer = '';
        $linesFound = array();
        $pos = $fileSize;
        $chunkSize = 4096;

        while (count($linesFound) < $lines && $pos > 0) {
            $readSize = min($chunkSize, $pos);
            $pos -= $readSize;
            
            fseek($handle, $pos);
            $chunk = fread($handle, $readSize);
            $buffer = $chunk . $buffer;
            
            $linesInBuffer = explode("\n", $buffer);
            
            if (count($linesInBuffer) > 1) {
                // Keep the first partial line for next iteration
                $buffer = array_shift($linesInBuffer);
                
                // Add lines to our result (in reverse order since we're reading backwards)
                $linesFound = array_merge($linesInBuffer, $linesFound);
                
                // Trim to requested number of lines
                if (count($linesFound) > $lines) {
                    $linesFound = array_slice($linesFound, -$lines);
                }
            }
        }

        // If we have a remaining buffer and need more lines, add it
        if (!empty($buffer) && count($linesFound) < $lines) {
            array_unshift($linesFound, $buffer);
        }

        // Remove empty last line if present
        if (end($linesFound) === '') {
            array_pop($linesFound);
        }

        return array(
            'lines' => $linesFound,
            'total_lines' => count($linesFound)
        );
    }
}

<?php
/**
 * PFG Log Reader Admin Configuration Fieldset
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log Viewer Configuration Fieldset Block
 *
 * Creates the custom log viewer interface within the system configuration.
 * Displays file list on the left and content viewer on the right.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Block_Adminhtml_System_Config_Form_Fieldset_Logviewer 
    extends Mage_Adminhtml_Block_System_Config_Form_Fieldset
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * Constructor
     */
    protected function _construct()
    {
        parent::_construct();
        $this->_helper = Mage::helper('pfg_logreader');
    }

    /**
     * Render fieldset html
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        if (!$this->_helper->isEnabled()) {
            return '<div class="pfg-logreader-disabled">
                <p><strong>Log Reader is disabled.</strong> Please enable it in General Settings above.</p>
            </div>';
        }

        $html = $this->_getHeaderHtml($element);
        $html .= $this->_getLogViewerHtml();
        $html .= $this->_getFooterHtml($element);
        $html .= $this->_getJavaScript();
        $html .= $this->_getCss();

        return $html;
    }

    /**
     * Get log viewer HTML
     *
     * @return string
     */
    protected function _getLogViewerHtml()
    {
        $ajaxUrl = $this->getUrl('adminhtml/pfg_logreader/ajax');
        $formKey = Mage::getSingleton('core/session')->getFormKey();

        return '
        <div class="pfg-logreader-container">
            <div class="pfg-logreader-left">
                <div class="pfg-logreader-header">
                    <h4>Log Files</h4>
                    <button type="button" id="pfg-logreader-refresh" class="scalable">
                        <span>Refresh</span>
                    </button>
                </div>
                <div id="pfg-logreader-files" class="pfg-logreader-files">
                    <div class="loading">Loading files...</div>
                </div>
                <div id="pfg-logreader-pagination" class="pfg-logreader-pagination"></div>
            </div>
            <div class="pfg-logreader-right">
                <div class="pfg-logreader-header">
                    <h4 id="pfg-logreader-file-title">File Content</h4>
                    <div class="pfg-logreader-file-info" id="pfg-logreader-file-info"></div>
                </div>
                <div class="pfg-logreader-content">
                    <textarea id="pfg-logreader-content" readonly placeholder="Select a file to view its content..."></textarea>
                </div>
            </div>
        </div>
        <input type="hidden" id="pfg-logreader-ajax-url" value="' . $ajaxUrl . '" />
        <input type="hidden" id="pfg-logreader-form-key" value="' . $formKey . '" />
        ';
    }

    /**
     * Get JavaScript for log viewer functionality
     *
     * @return string
     */
    protected function _getJavaScript()
    {
        return '
        <script type="text/javascript">
        //<![CDATA[
        var PfgLogReader = {
            currentPage: 1,
            ajaxUrl: null,
            formKey: null,
            
            init: function() {
                this.ajaxUrl = document.getElementById("pfg-logreader-ajax-url").value;
                this.formKey = document.getElementById("pfg-logreader-form-key").value;
                
                // Bind events
                document.getElementById("pfg-logreader-refresh").onclick = this.refreshFiles.bind(this);
                
                // Load initial files
                this.loadFiles(1);
            },
            
            loadFiles: function(page) {
                this.currentPage = page;
                var filesContainer = document.getElementById("pfg-logreader-files");
                filesContainer.innerHTML = "<div class=\"loading\">Loading files...</div>";
                
                new Ajax.Request(this.ajaxUrl, {
                    method: "post",
                    parameters: {
                        action: "list_files",
                        page: page,
                        form_key: this.formKey
                    },
                    onSuccess: this.onFilesLoaded.bind(this),
                    onFailure: this.onError.bind(this)
                });
            },
            
            onFilesLoaded: function(response) {
                try {
                    var data = response.responseJSON || response.responseText.evalJSON();
                    
                    if (data.success) {
                        this.renderFiles(data.files);
                        this.renderPagination(data.pagination);
                    } else {
                        this.showError(data.error || "Failed to load files");
                    }
                } catch (e) {
                    this.showError("Invalid response from server");
                }
            },
            
            renderFiles: function(files) {
                var html = "";
                
                if (files.length === 0) {
                    html = "<div class=\"no-files\">No log files found</div>";
                } else {
                    files.each(function(file) {
                        var cssClass = file.readable ? "file-item" : "file-item disabled";
                        var onclick = file.readable ? "PfgLogReader.loadFileContent(\'" + file.path + "\', \'" + file.name + "\')" : "";
                        
                        html += "<div class=\"" + cssClass + "\" onclick=\"" + onclick + "\">";
                        html += "<div class=\"file-name\">" + file.name + "</div>";
                        if (file.directory) {
                            html += "<div class=\"file-directory\">" + file.directory + "</div>";
                        }
                        html += "<div class=\"file-details\">";
                        html += "<span class=\"file-size\">" + file.size_formatted + "</span>";
                        html += "<span class=\"file-modified\">" + file.modified_formatted + "</span>";
                        html += "<span class=\"file-type\">" + file.type + "</span>";
                        html += "</div>";
                        if (!file.readable) {
                            html += "<div class=\"file-error\">File too large or not readable</div>";
                        }
                        html += "</div>";
                    });
                }
                
                document.getElementById("pfg-logreader-files").innerHTML = html;
            },
            
            renderPagination: function(pagination) {
                var html = "";
                
                if (pagination.total_pages > 1) {
                    html += "<div class=\"pagination-info\">";
                    html += "Page " + pagination.current_page + " of " + pagination.total_pages;
                    html += " (" + pagination.total_files + " files total)";
                    html += "</div>";
                    
                    html += "<div class=\"pagination-controls\">";
                    
                    if (pagination.has_previous) {
                        html += "<button type=\"button\" onclick=\"PfgLogReader.loadFiles(" + (pagination.current_page - 1) + ")\">Previous</button>";
                    }
                    
                    if (pagination.has_next) {
                        html += "<button type=\"button\" onclick=\"PfgLogReader.loadFiles(" + (pagination.current_page + 1) + ")\">Next</button>";
                    }
                    
                    html += "</div>";
                }
                
                document.getElementById("pfg-logreader-pagination").innerHTML = html;
            },
            
            loadFileContent: function(filePath, fileName) {
                var contentArea = document.getElementById("pfg-logreader-content");
                var fileTitle = document.getElementById("pfg-logreader-file-title");
                var fileInfo = document.getElementById("pfg-logreader-file-info");
                
                contentArea.value = "Loading file content...";
                fileTitle.innerHTML = "Loading: " + fileName;
                fileInfo.innerHTML = "";
                
                new Ajax.Request(this.ajaxUrl, {
                    method: "post",
                    parameters: {
                        action: "read_file",
                        file_path: filePath,
                        form_key: this.formKey
                    },
                    onSuccess: this.onFileContentLoaded.bind(this, fileName),
                    onFailure: this.onError.bind(this)
                });
            },
            
            onFileContentLoaded: function(fileName, response) {
                try {
                    var data = response.responseJSON || response.responseText.evalJSON();
                    
                    if (data.success) {
                        document.getElementById("pfg-logreader-content").value = data.lines.join("\\n");
                        document.getElementById("pfg-logreader-file-title").innerHTML = fileName;
                        document.getElementById("pfg-logreader-file-info").innerHTML = 
                            "Showing last " + data.total_lines + " lines | File size: " + data.file_size_formatted;
                    } else {
                        this.showError(data.error || "Failed to load file content");
                        document.getElementById("pfg-logreader-content").value = "Error: " + (data.error || "Failed to load file");
                    }
                } catch (e) {
                    this.showError("Invalid response from server");
                }
            },
            
            refreshFiles: function() {
                this.loadFiles(this.currentPage);
            },
            
            onError: function(response) {
                this.showError("Network error occurred");
            },
            
            showError: function(message) {
                document.getElementById("pfg-logreader-files").innerHTML = 
                    "<div class=\"error\">Error: " + message + "</div>";
            }
        };
        
        // Initialize when DOM is ready
        document.observe("dom:loaded", function() {
            PfgLogReader.init();
        });
        //]]>
        </script>';
    }

    /**
     * Get CSS styles for log viewer
     *
     * @return string
     */
    protected function _getCss()
    {
        return '
        <style type="text/css">
        .pfg-logreader-container {
            display: flex;
            gap: 20px;
            min-height: 600px;
            margin: 20px 0;
        }
        
        .pfg-logreader-left {
            flex: 1;
            min-width: 300px;
        }
        
        .pfg-logreader-right {
            flex: 2;
            min-width: 400px;
        }
        
        .pfg-logreader-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .pfg-logreader-header h4 {
            margin: 0;
            color: #333;
        }
        
        .pfg-logreader-files {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            background: #fff;
        }
        
        .file-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .file-item:hover {
            background-color: #f5f5f5;
        }
        
        .file-item.disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .file-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .file-directory {
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .file-details {
            font-size: 11px;
            color: #888;
        }
        
        .file-details span {
            margin-right: 15px;
        }
        
        .file-error {
            font-size: 11px;
            color: #d40707;
            margin-top: 5px;
        }
        
        .pfg-logreader-content textarea {
            width: 100%;
            height: 400px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ddd;
            padding: 10px;
            resize: vertical;
        }
        
        .pfg-logreader-pagination {
            margin-top: 10px;
            text-align: center;
        }
        
        .pagination-info {
            margin-bottom: 10px;
            font-size: 12px;
            color: #666;
        }
        
        .pagination-controls button {
            margin: 0 5px;
        }
        
        .pfg-logreader-file-info {
            font-size: 12px;
            color: #666;
        }
        
        .loading, .error, .no-files {
            padding: 20px;
            text-align: center;
            color: #666;
        }
        
        .error {
            color: #d40707;
        }
        
        .pfg-logreader-disabled {
            padding: 20px;
            background: #f5f5f5;
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .pfg-logreader-container {
                flex-direction: column;
            }
            
            .pfg-logreader-left,
            .pfg-logreader-right {
                min-width: auto;
            }
        }
        </style>';
    }
}

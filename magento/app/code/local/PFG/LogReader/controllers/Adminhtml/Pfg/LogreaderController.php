<?php
/**
 * PFG Log Reader Admin Controller
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log Reader Admin Controller
 *
 * Handles AJAX requests for file listing and content reading.
 * Includes CSRF protection and comprehensive error handling.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Adminhtml_Pfg_LogreaderController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * File model instance
     *
     * @var PFG_LogReader_Model_File
     */
    protected $_fileModel;

    /**
     * Constructor
     */
    protected function _construct()
    {
        parent::_construct();
        $this->_helper = Mage::helper('pfg_logreader');
        $this->_fileModel = Mage::getModel('pfg_logreader/file');
    }

    /**
     * AJAX endpoint for file operations
     */
    public function ajaxAction()
    {
        // Verify CSRF token
        if (!$this->_validateFormKey()) {
            $this->_returnJsonError('Invalid form key');
            return;
        }

        // Check if module is enabled
        if (!$this->_helper->isEnabled()) {
            $this->_returnJsonError('Log Reader module is disabled');
            return;
        }

        // Check admin permissions
        if (!$this->_isAllowed()) {
            $this->_returnJsonError('Access denied');
            return;
        }

        $action = $this->getRequest()->getParam('action');

        try {
            switch ($action) {
                case 'list_files':
                    $this->_handleListFiles();
                    break;
                    
                case 'read_file':
                    $this->_handleReadFile();
                    break;
                    
                default:
                    $this->_returnJsonError('Invalid action');
                    break;
            }
        } catch (Exception $e) {
            $this->_helper->logError('AJAX action error', $e);
            $this->_returnJsonError('An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Handle file listing request
     */
    protected function _handleListFiles()
    {
        $page = (int) $this->getRequest()->getParam('page', 1);
        $page = max(1, $page); // Ensure page is at least 1

        $result = $this->_fileModel->discoverFiles($page);

        if (isset($result['error'])) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'files' => $result['files'],
            'pagination' => $result['pagination']
        ));
    }

    /**
     * Handle file content reading request
     */
    protected function _handleReadFile()
    {
        $filePath = $this->getRequest()->getParam('file_path');

        if (empty($filePath)) {
            $this->_returnJsonError('File path is required');
            return;
        }

        // Security check: ensure file path is within allowed directories
        if (!$this->_isPathAllowed($filePath)) {
            $this->_helper->log('Attempted access to unauthorized file: ' . $filePath);
            $this->_returnJsonError('Access to this file is not allowed');
            return;
        }

        $result = $this->_fileModel->readLastLines($filePath);

        if (!$result['success']) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'lines' => $result['lines'],
            'total_lines' => $result['total_lines'],
            'file_size' => $result['file_size'],
            'file_size_formatted' => $result['file_size_formatted']
        ));
    }

    /**
     * Check if file path is within allowed directories
     *
     * @param string $filePath
     * @return bool
     */
    protected function _isPathAllowed($filePath)
    {
        $realPath = realpath($filePath);
        if ($realPath === false) {
            return false;
        }

        $logDir = realpath($this->_helper->getLogDirectory());
        $reportDir = realpath($this->_helper->getReportDirectory());

        // Check if file is within log or report directory
        return (strpos($realPath, $logDir) === 0) || (strpos($realPath, $reportDir) === 0);
    }

    /**
     * Return JSON success response
     *
     * @param array $data
     */
    protected function _returnJsonSuccess($data = array())
    {
        $response = array_merge(array('success' => true), $data);
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }

    /**
     * Return JSON error response
     *
     * @param string $message
     */
    protected function _returnJsonError($message)
    {
        $response = array(
            'success' => false,
            'error' => $message
        );
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }

    /**
     * Check admin permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('system/config/pfg_logreader');
    }
}
